package handlers

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"ai-api-gateway/internal/application/dto"
	"ai-api-gateway/internal/application/services"
	"ai-api-gateway/internal/domain/entities"
	"ai-api-gateway/internal/domain/repositories"
	"ai-api-gateway/internal/infrastructure/clients"
	"ai-api-gateway/internal/infrastructure/config"
	"ai-api-gateway/internal/infrastructure/functioncall"
	"ai-api-gateway/internal/infrastructure/gateway"
	"ai-api-gateway/internal/infrastructure/logger"
	"ai-api-gateway/internal/presentation/middleware"

	"github.com/gin-gonic/gin"
)

// AIHandler AI请求处理器
type AIHandler struct {
	gatewayService           gateway.GatewayService
	modelService             services.ModelService
	usageLogService          services.UsageLogService
	logger                   logger.Logger
	config                   *config.Config
	functionCallHandler      functioncall.FunctionCallHandler
	providerModelSupportRepo repositories.ProviderModelSupportRepository
	httpClient               clients.HTTPClient
	aiClient                 clients.AIProviderClient
}

// NewAIHandler 创建AI请求处理器
func NewAIHandler(
	gatewayService gateway.GatewayService,
	modelService services.ModelService,
	usageLogService services.UsageLogService,
	logger logger.Logger,
	config *config.Config,
	functionCallHandler functioncall.FunctionCallHandler,
	providerModelSupportRepo repositories.ProviderModelSupportRepository,
	httpClient clients.HTTPClient,
	aiClient clients.AIProviderClient,
) *AIHandler {
	return &AIHandler{
		gatewayService:           gatewayService,
		modelService:             modelService,
		usageLogService:          usageLogService,
		logger:                   logger,
		config:                   config,
		functionCallHandler:      functionCallHandler,
		providerModelSupportRepo: providerModelSupportRepo,
		httpClient:               httpClient,
		aiClient:                 aiClient,
	}
}

// handleStreamingRequest 处理流式请求
func (h *AIHandler) handleStreamingRequest(c *gin.Context, gatewayRequest *gateway.GatewayRequest, requestID string, userID, apiKeyID int64) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Request-ID", requestID)

	// 获取响应写入器
	w := c.Writer

	// 检查是否启用了Function Call
	if h.config.FunctionCall.Enabled && len(gatewayRequest.Request.Tools) > 0 {
		// 对于有工具的流式请求，需要特殊处理
		h.handleStreamingRequestWithFunctionCall(c, gatewayRequest, requestID, userID, apiKeyID)
		return
	}

	// 创建流式响应通道
	streamChan := make(chan *gateway.StreamChunk, 100)
	errorChan := make(chan error, 1)

	// 启动流式处理
	go func() {
		defer func() {
			// 安全关闭channels
			select {
			case <-streamChan:
			default:
				close(streamChan)
			}

			select {
			case <-errorChan:
			default:
				close(errorChan)
			}
		}()

		err := h.gatewayService.ProcessStreamRequest(c.Request.Context(), gatewayRequest, streamChan)
		if err != nil {
			select {
			case errorChan <- err:
			case <-c.Request.Context().Done():
				// 如果上下文已取消，不发送错误
			}
		}
	}()

	// 发送流式数据
	var totalTokens int
	var totalCost float64

	for {
		select {
		case chunk, ok := <-streamChan:
			if !ok {
				// 流结束，发送结束标记
				_, err := w.Write([]byte("data: [DONE]\n\n"))
				if err != nil {
					h.logger.WithFields(map[string]interface{}{
						"request_id": requestID,
						"error":      err.Error(),
					}).Error("Failed to write stream end marker")
				}
				w.Flush()

				// 输出流式AI提供商响应结果
				h.logger.WithFields(map[string]interface{}{
					"request_id":   requestID,
					"user_id":      userID,
					"api_key_id":   apiKeyID,
					"total_tokens": totalTokens,
					"total_cost":   totalCost,
					"stream_type":  "completed",
				}).Info("AI provider streaming response completed successfully")

				// 设置使用量到上下文
				c.Set("tokens_used", totalTokens)
				c.Set("cost_used", totalCost)
				return
			}

			// 累计使用量
			if chunk.Usage != nil {
				totalTokens += chunk.Usage.TotalTokens
			}
			if chunk.Cost != nil {
				totalCost += chunk.Cost.TotalCost
			}

			// 构造SSE数据
			data := map[string]interface{}{
				"id":      chunk.ID,
				"object":  "chat.completion.chunk",
				"created": chunk.Created,
				"model":   chunk.Model,
				"choices": []map[string]interface{}{
					{
						"index": 0,
						"delta": map[string]interface{}{
							"content": chunk.Content,
						},
						"finish_reason": chunk.FinishReason,
					},
				},
			}

			// 序列化为JSON
			jsonData, err := json.Marshal(data)
			if err != nil {
				h.logger.WithFields(map[string]interface{}{
					"request_id": requestID,
					"error":      err.Error(),
				}).Error("Failed to marshal stream chunk")
				continue
			}

			// 发送SSE数据
			sseMessage := fmt.Sprintf("data: %s\n\n", jsonData)
			_, err = w.Write([]byte(sseMessage))
			if err != nil {
				h.logger.WithFields(map[string]interface{}{
					"request_id": requestID,
					"error":      err.Error(),
				}).Error("Failed to write stream chunk")
				return
			}

			// 立即刷新缓冲区
			w.Flush()

		case err := <-errorChan:
			if err != nil {
				h.logger.WithFields(map[string]interface{}{
					"request_id": requestID,
					"user_id":    userID,
					"api_key_id": apiKeyID,
					"error":      err.Error(),
				}).Error("Stream processing failed")

				// 发送错误事件
				errorData := map[string]interface{}{
					"error": map[string]interface{}{
						"message": "Stream processing failed",
						"type":    "server_error",
						"code":    "stream_error",
					},
				}

				jsonData, _ := json.Marshal(errorData)
				w.Write([]byte(fmt.Sprintf("data: %s\n\n", jsonData)))
				w.Flush()
			}
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			h.logger.WithFields(map[string]interface{}{
				"request_id": requestID,
			}).Info("Client disconnected from stream")
			return
		}
	}
}

// ChatCompletions 处理聊天完成请求
// @Summary 聊天补全
// @Description 创建聊天补全请求，兼容OpenAI API格式。支持流式和非流式响应。
// @Tags AI接口
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body clients.ChatCompletionRequest true "聊天补全请求"
// @Success 200 {object} clients.AIResponse "聊天补全响应"
// @Failure 400 {object} dto.Response "请求参数错误"
// @Failure 401 {object} dto.Response "认证失败"
// @Failure 429 {object} dto.Response "请求过于频繁"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /v1/chat/completions [post]
func (h *AIHandler) ChatCompletions(c *gin.Context) {
	// 获取认证信息
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"AUTHENTICATION_REQUIRED",
			"Authentication required",
			nil,
		))
		return
	}

	apiKeyID, exists := middleware.GetAPIKeyIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"AUTHENTICATION_REQUIRED",
			"API key required",
			nil,
		))
		return
	}

	// 解析请求体
	var chatRequest clients.ChatCompletionRequest
	if err := c.ShouldBindJSON(&chatRequest); err != nil {
		h.logger.WithFields(map[string]interface{}{
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"error":      err.Error(),
		}).Warn("Invalid request body")

		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request body",
			map[string]interface{}{
				"details": err.Error(),
			},
		))
		return
	}

	// 验证必需字段
	if chatRequest.Model == "" {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"MISSING_MODEL",
			"Model is required",
			nil,
		))
		return
	}

	if len(chatRequest.Messages) == 0 {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"MISSING_MESSAGES",
			"Messages array is required for chat completions",
			nil,
		))
		return
	}

	// 获取请求ID
	requestID := middleware.GetRequestIDFromContext(c)

	// 转换为通用 AIRequest 结构
	aiRequest := &clients.AIRequest{
		Model:       chatRequest.Model,
		Messages:    chatRequest.Messages,
		MaxTokens:   chatRequest.MaxTokens,
		Temperature: chatRequest.Temperature,
		Stream:      chatRequest.Stream,
		Tools:       chatRequest.Tools,
		ToolChoice:  chatRequest.ToolChoice,
		WebSearch:   chatRequest.WebSearch,
	}

	// 如果开启了联网搜索且没有提供工具，自动添加可用工具
	if h.functionCallHandler != nil && len(aiRequest.Tools) == 0 && aiRequest.WebSearch {
		aiRequest.Tools = h.functionCallHandler.GetAvailableTools()
		aiRequest.ToolChoice = "auto"
	}

	// 构造网关请求
	gatewayRequest := &gateway.GatewayRequest{
		UserID:    userID,
		APIKeyID:  apiKeyID,
		ModelSlug: aiRequest.Model,
		Request:   aiRequest,
		RequestID: requestID,
	}

	// 检查是否为流式请求
	if aiRequest.Stream {
		h.handleStreamingRequest(c, gatewayRequest, requestID, userID, apiKeyID)
		return
	}

	// 处理非流式请求
	response, err := h.gatewayService.ProcessRequest(c.Request.Context(), gatewayRequest)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"model":      aiRequest.Model,
			"error":      err.Error(),
		}).Error("Failed to process AI request")

		c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"REQUEST_FAILED",
			"Failed to process request",
			map[string]interface{}{
				"request_id": requestID,
			},
		))
		return
	}

	// 输出AI提供商响应结果
	h.logger.WithFields(map[string]interface{}{
		"request_id":    requestID,
		"user_id":       userID,
		"api_key_id":    apiKeyID,
		"model":         aiRequest.Model,
		"provider":      response.Provider,
		"duration_ms":   response.Duration.Milliseconds(),
		"total_tokens":  response.Usage.TotalTokens,
		"input_tokens":  response.Usage.InputTokens,
		"output_tokens": response.Usage.OutputTokens,
		"total_cost":    response.Cost.TotalCost,
		"response_data": response.Response,
	}).Info("AI provider response received successfully")

	// 设置使用量到上下文（用于配额中间件）
	c.Set("tokens_used", response.Usage.TotalTokens)
	c.Set("cost_used", response.Cost.TotalCost)

	// 设置响应头
	c.Header("X-Request-ID", requestID)
	c.Header("X-Provider", response.Provider)
	c.Header("X-Model", response.Model)
	c.Header("X-Duration-Ms", strconv.FormatInt(response.Duration.Milliseconds(), 10))

	// 检查是否需要处理 Function Call
	if h.functionCallHandler != nil && response.Response != nil {
		finalResponse, err := h.handleFunctionCallResponse(c.Request.Context(), response.Response, aiRequest, gatewayRequest)
		if err != nil {
			h.logger.WithFields(map[string]interface{}{
				"request_id": requestID,
				"error":      err.Error(),
			}).Error("Failed to handle function call")

			c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"FUNCTION_CALL_FAILED",
				"Failed to process function call",
				map[string]interface{}{
					"request_id": requestID,
				},
			))
			return
		}

		if finalResponse != nil {
			c.JSON(http.StatusOK, finalResponse)
			return
		}
	}

	// 返回AI响应（保持与OpenAI API兼容的格式）
	c.JSON(http.StatusOK, response.Response)
}

// Completions 处理文本完成请求
// @Summary 文本补全
// @Description 创建文本补全请求，兼容OpenAI API格式
// @Tags AI接口
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body clients.CompletionRequest true "文本补全请求"
// @Success 200 {object} clients.AIResponse "文本补全响应"
// @Failure 400 {object} dto.Response "请求参数错误"
// @Failure 401 {object} dto.Response "认证失败"
// @Failure 429 {object} dto.Response "请求过于频繁"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /v1/completions [post]
func (h *AIHandler) Completions(c *gin.Context) {
	// 获取认证信息
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"AUTHENTICATION_REQUIRED",
			"Authentication required",
			nil,
		))
		return
	}

	apiKeyID, exists := middleware.GetAPIKeyIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"AUTHENTICATION_REQUIRED",
			"API key required",
			nil,
		))
		return
	}

	// 解析请求体
	var completionRequest clients.CompletionRequest
	if err := c.ShouldBindJSON(&completionRequest); err != nil {
		h.logger.WithFields(map[string]interface{}{
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"error":      err.Error(),
		}).Warn("Invalid request body")

		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request body",
			map[string]interface{}{
				"details": err.Error(),
			},
		))
		return
	}

	// 验证必需字段
	if completionRequest.Model == "" {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"MISSING_MODEL",
			"Model is required",
			nil,
		))
		return
	}

	if completionRequest.Prompt == "" {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"MISSING_PROMPT",
			"Prompt is required",
			nil,
		))
		return
	}

	// 获取请求ID
	requestID := middleware.GetRequestIDFromContext(c)

	// 转换为通用 AIRequest 结构
	aiRequest := &clients.AIRequest{
		Model:       completionRequest.Model,
		Prompt:      completionRequest.Prompt,
		MaxTokens:   completionRequest.MaxTokens,
		Temperature: completionRequest.Temperature,
		Stream:      completionRequest.Stream,
		WebSearch:   completionRequest.WebSearch,
	}

	// 如果开启了联网搜索，自动添加可用工具
	if h.functionCallHandler != nil && aiRequest.WebSearch {
		// 将 prompt 转换为 messages 格式以支持 function call
		aiRequest.Messages = []clients.AIMessage{
			{
				Role:    "user",
				Content: aiRequest.Prompt,
			},
		}
		aiRequest.Prompt = "" // 清空 prompt，使用 messages
		aiRequest.Tools = h.functionCallHandler.GetAvailableTools()
		aiRequest.ToolChoice = "auto"
	}

	// 构造网关请求
	gatewayRequest := &gateway.GatewayRequest{
		UserID:    userID,
		APIKeyID:  apiKeyID,
		ModelSlug: aiRequest.Model,
		Request:   aiRequest,
		RequestID: requestID,
	}

	// 处理请求
	response, err := h.gatewayService.ProcessRequest(c.Request.Context(), gatewayRequest)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"model":      aiRequest.Model,
			"error":      err.Error(),
		}).Error("Failed to process AI request")

		c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"REQUEST_FAILED",
			"Failed to process request",
			map[string]interface{}{
				"request_id": requestID,
			},
		))
		return
	}

	// 输出AI提供商响应结果
	h.logger.WithFields(map[string]interface{}{
		"request_id":    requestID,
		"user_id":       userID,
		"api_key_id":    apiKeyID,
		"model":         aiRequest.Model,
		"provider":      response.Provider,
		"duration_ms":   response.Duration.Milliseconds(),
		"total_tokens":  response.Usage.TotalTokens,
		"input_tokens":  response.Usage.InputTokens,
		"output_tokens": response.Usage.OutputTokens,
		"total_cost":    response.Cost.TotalCost,
		"response_data": response.Response,
	}).Info("AI provider response received successfully")

	// 设置使用量到上下文（用于配额中间件）
	c.Set("tokens_used", response.Usage.TotalTokens)
	c.Set("cost_used", response.Cost.TotalCost)

	// 设置响应头
	c.Header("X-Request-ID", requestID)
	c.Header("X-Provider", response.Provider)
	c.Header("X-Model", response.Model)
	c.Header("X-Duration-Ms", strconv.FormatInt(response.Duration.Milliseconds(), 10))

	// 返回AI响应
	c.JSON(http.StatusOK, response.Response)
}

// Models 获取可用模型列表
// @Summary 列出模型
// @Description 获取可用的AI模型列表
// @Tags AI接口
// @Produce json
// @Security BearerAuth
// @Success 200 {object} clients.ModelsResponse "模型列表"
// @Failure 401 {object} dto.Response "认证失败"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /v1/models [get]
func (h *AIHandler) Models(c *gin.Context) {
	// 获取可用模型列表
	models, err := h.modelService.GetAvailableModels(c.Request.Context(), 0) // 0 表示获取所有提供商的模型
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
		}).Error("Failed to get available models")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"message": "Failed to get models",
				"type":    "internal_error",
				"code":    "models_fetch_failed",
			},
		})
		return
	}

	// 转换为 OpenAI API 格式
	var modelList []map[string]interface{}
	for _, model := range models {
		displayName := model.Name
		if model.DisplayName != nil {
			displayName = *model.DisplayName
		}

		modelData := map[string]interface{}{
			"id":       model.Slug,
			"object":   "model",
			"created":  model.CreatedAt.Unix(),
			"owned_by": "system",
		}

		// 添加可选字段
		if model.Description != nil {
			modelData["description"] = *model.Description
		}

		// 添加扩展信息
		modelData["display_name"] = displayName
		modelData["model_type"] = string(model.ModelType)
		modelData["status"] = string(model.Status)

		if model.ContextLength != nil {
			modelData["context_length"] = *model.ContextLength
		}

		if model.MaxTokens != nil {
			modelData["max_tokens"] = *model.MaxTokens
		}

		modelData["supports_streaming"] = model.SupportsStreaming
		modelData["supports_functions"] = model.SupportsFunctions

		modelList = append(modelList, modelData)
	}

	// 返回 OpenAI API 兼容格式
	c.JSON(http.StatusOK, gin.H{
		"object": "list",
		"data":   modelList,
	})
}

// Usage 获取使用情况
// @Summary 使用统计
// @Description 获取当前用户的API使用统计
// @Tags AI接口
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.UsageResponse "使用统计信息"
// @Failure 401 {object} dto.Response "认证失败"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /v1/usage [get]
func (h *AIHandler) Usage(c *gin.Context) {
	// 获取认证信息
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"AUTHENTICATION_REQUIRED",
			"Authentication required",
			nil,
		))
		return
	}

	// 获取使用统计
	stats, err := h.usageLogService.GetUsageStats(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"user_id": userID,
			"error":   err.Error(),
		}).Error("Failed to get usage stats")

		c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"USAGE_STATS_ERROR",
			"Failed to get usage statistics",
			nil,
		))
		return
	}

	// 构造响应数据
	usageResponse := dto.UsageResponse{
		TotalRequests: int(stats.TotalRequests),
		TotalTokens:   int(stats.TotalTokens),
		TotalCost:     stats.TotalCost,
	}

	c.JSON(http.StatusOK, dto.SuccessResponse(usageResponse, "Usage statistics retrieved successfully"))
}

// handleFunctionCallResponse 处理包含 Function Call 的响应
func (h *AIHandler) handleFunctionCallResponse(ctx context.Context, response *clients.AIResponse, originalRequest *clients.AIRequest, gatewayRequest *gateway.GatewayRequest) (*clients.AIResponse, error) {
	// 检查响应中是否包含工具调用
	if len(response.Choices) == 0 {
		return nil, nil
	}

	choice := response.Choices[0]

	// 检查是否有工具调用
	var toolCalls []clients.ToolCall
	if len(choice.Message.ToolCalls) > 0 {
		toolCalls = choice.Message.ToolCalls
	} else if len(choice.ToolCalls) > 0 {
		toolCalls = choice.ToolCalls
	}

	if len(toolCalls) == 0 {
		return nil, nil // 没有工具调用，返回原响应
	}

	h.logger.WithFields(map[string]interface{}{
		"tool_calls_count": len(toolCalls),
		"request_id":       gatewayRequest.RequestID,
	}).Info("Processing function calls")

	// 将助手的消息（包含工具调用）添加到消息历史
	messages := append(originalRequest.Messages, choice.Message)

	// 执行工具调用
	toolMessages, err := h.functionCallHandler.HandleFunctionCalls(ctx, messages, toolCalls)
	if err != nil {
		return nil, fmt.Errorf("failed to handle function calls: %w", err)
	}

	// 将工具响应消息添加到消息历史
	messages = append(messages, toolMessages...)

	// 创建新的请求，包含完整的消息历史
	newRequest := &clients.AIRequest{
		Model:       originalRequest.Model,
		Messages:    messages,
		MaxTokens:   originalRequest.MaxTokens,
		Temperature: originalRequest.Temperature,
		Stream:      false, // Function call 后的请求不使用流式
		// 不再包含 tools，让模型生成最终回复
	}

	// 创建新的网关请求
	newGatewayRequest := &gateway.GatewayRequest{
		UserID:    gatewayRequest.UserID,
		APIKeyID:  gatewayRequest.APIKeyID,
		ModelSlug: gatewayRequest.ModelSlug,
		Request:   newRequest,
		RequestID: gatewayRequest.RequestID,
	}

	// 发送第二次请求获取最终回复
	finalResponse, err := h.gatewayService.ProcessRequest(ctx, newGatewayRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to process final request after function calls: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"request_id":   gatewayRequest.RequestID,
		"final_tokens": finalResponse.Usage.TotalTokens,
		"final_cost":   finalResponse.Cost.TotalCost,
	}).Info("Function call processing completed")

	return finalResponse.Response, nil
}

// handleStreamingRequestWithFunctionCall 处理带有Function Call的流式请求
func (h *AIHandler) handleStreamingRequestWithFunctionCall(c *gin.Context, gatewayRequest *gateway.GatewayRequest, requestID string, userID, apiKeyID int64) {
	w := c.Writer

	h.logger.WithFields(map[string]interface{}{
		"request_id":  requestID,
		"tools_count": len(gatewayRequest.Request.Tools),
	}).Info("Processing streaming request with function call support")

	// 首先发送非流式请求来检查是否有tool calls
	nonStreamRequest := *gatewayRequest.Request
	nonStreamRequest.Stream = false

	nonStreamGatewayRequest := &gateway.GatewayRequest{
		UserID:    gatewayRequest.UserID,
		APIKeyID:  gatewayRequest.APIKeyID,
		ModelSlug: gatewayRequest.ModelSlug,
		Request:   &nonStreamRequest,
		RequestID: gatewayRequest.RequestID,
	}

	// 处理第一次请求
	response, err := h.gatewayService.ProcessRequest(c.Request.Context(), nonStreamGatewayRequest)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"error":      err.Error(),
		}).Error("Failed to process initial request for function call detection")

		// 发送错误事件
		errorData := map[string]interface{}{
			"error": map[string]interface{}{
				"message": "Failed to process request",
				"type":    "internal_error",
			},
		}
		errorJSON, _ := json.Marshal(errorData)
		w.Write([]byte(fmt.Sprintf("data: %s\n\n", errorJSON)))
		if flusher, ok := w.(http.Flusher); ok {
			flusher.Flush()
		}
		return
	}

	// 检查是否需要处理 Function Call
	if h.functionCallHandler != nil && response.Response != nil {
		finalResponse, err := h.handleFunctionCallResponse(c.Request.Context(), response.Response, &nonStreamRequest, nonStreamGatewayRequest)
		if err != nil {
			h.logger.WithFields(map[string]interface{}{
				"request_id": requestID,
				"error":      err.Error(),
			}).Error("Failed to handle function call in streaming request")

			// 发送错误事件
			errorData := map[string]interface{}{
				"error": map[string]interface{}{
					"message": "Failed to process function call",
					"type":    "function_call_error",
				},
			}
			errorJSON, _ := json.Marshal(errorData)
			w.Write([]byte(fmt.Sprintf("data: %s\n\n", errorJSON)))
			if flusher, ok := w.(http.Flusher); ok {
				flusher.Flush()
			}
			return
		}

		// 如果有function call处理结果，使用最终响应
		if finalResponse != nil {
			response.Response = finalResponse
		}
	}

	// 现在以流式方式发送最终响应
	if response.Response != nil && len(response.Response.Choices) > 0 {
		choice := response.Response.Choices[0]
		content := choice.Message.Content

		// 将内容分块发送，模拟流式输出
		h.streamContent(w, content, response.Response.ID, response.Response.Model, requestID)
	}

	// 发送结束标记
	w.Write([]byte("data: [DONE]\n\n"))
	if flusher, ok := w.(http.Flusher); ok {
		flusher.Flush()
	}

	// 设置使用量到上下文
	if response.Usage != nil {
		c.Set("tokens_used", response.Usage.TotalTokens)
	}
	if response.Cost != nil {
		c.Set("cost_used", response.Cost.TotalCost)
	}
}

// streamContent 将内容以流式方式发送
func (h *AIHandler) streamContent(w http.ResponseWriter, content, responseID, model, requestID string) {
	// 获取Flusher接口
	flusher, ok := w.(http.Flusher)
	if !ok {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
		}).Error("ResponseWriter does not support flushing")
		return
	}

	// 将内容按字符分块发送
	for i, char := range content {
		chunk := map[string]interface{}{
			"id":      responseID,
			"object":  "chat.completion.chunk",
			"created": time.Now().Unix(),
			"model":   model,
			"choices": []map[string]interface{}{
				{
					"index": 0,
					"delta": map[string]interface{}{
						"content": string(char),
					},
					"finish_reason": func() interface{} {
						if i == len(content)-1 {
							return "stop"
						}
						return nil
					}(),
				},
			},
		}

		chunkJSON, err := json.Marshal(chunk)
		if err != nil {
			h.logger.WithFields(map[string]interface{}{
				"request_id": requestID,
				"error":      err.Error(),
			}).Error("Failed to marshal stream chunk")
			continue
		}

		w.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkJSON)))
		flusher.Flush()

		// 添加小延迟以模拟真实的流式输出
		time.Sleep(10 * time.Millisecond)
	}
}

// AnthropicMessages 处理Anthropic Messages API请求
// @Summary Anthropic Messages API - 创建消息
// @Description 完全兼容 Anthropic Messages API 的消息创建接口。支持文本对话、工具调用、流式响应等功能。
// @Description
// @Description **支持的功能：**
// @Description - 文本对话（单轮和多轮）
// @Description - 系统提示（system prompt）
// @Description - 流式响应（Server-Sent Events）
// @Description - 工具调用（Function Calling）
// @Description - 温度控制、Top-K、Top-P 采样
// @Description - 停止序列、最大token限制
// @Description
// @Description **认证方式：**
// @Description - Bearer Token: `Authorization: Bearer YOUR_API_KEY`
// @Description - API Key Header: `x-api-key: YOUR_API_KEY`
// @Description
// @Description **版本控制：**
// @Description - 推荐添加版本头: `anthropic-version: 2023-06-01`
// @Description
// @Description **流式响应：**
// @Description - 设置 `stream: true` 启用流式响应
// @Description - 响应格式为 Server-Sent Events (text/event-stream)
// @Description - 每个数据块以 `data: ` 开头，结束时发送 `data: [DONE]`
// @Tags AI接口
// @Accept json
// @Produce json
// @Produce text/event-stream
// @Security BearerAuth
// @Param anthropic-version header string false "Anthropic API版本" default(2023-06-01)
// @Param x-api-key header string false "API密钥（可替代Authorization头）"
// @Param body body clients.AnthropicMessageRequest true "Anthropic消息请求"
// @Success 200 {object} clients.AnthropicMessageResponse "成功响应"
// @Success 200 {string} string "流式响应 (当stream=true时)" format(text/event-stream)
// @Failure 400 {object} object "请求参数错误" example({"type":"error","error":{"type":"invalid_request_error","message":"model is required"}})
// @Failure 401 {object} object "认证失败" example({"type":"error","error":{"type":"authentication_error","message":"Authentication required"}})
// @Failure 429 {object} object "请求过于频繁" example({"type":"error","error":{"type":"rate_limit_error","message":"Rate limit exceeded"}})
// @Failure 500 {object} object "服务器内部错误" example({"type":"error","error":{"type":"api_error","message":"Internal server error"}})
// @Router /v1/messages [post]
func (h *AIHandler) AnthropicMessages(c *gin.Context) {
	// 获取认证信息
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "authentication_error",
				"message": "Authentication required",
			},
		})
		return
	}

	apiKeyID, exists := middleware.GetAPIKeyIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "authentication_error",
				"message": "API key required",
			},
		})
		return
	}

	// 解析请求体
	var anthropicRequest clients.AnthropicMessageRequest
	if err := c.ShouldBindJSON(&anthropicRequest); err != nil {
		h.logger.WithFields(map[string]interface{}{
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"error":      err.Error(),
		}).Warn("Invalid request body")

		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "Invalid request body: " + err.Error(),
			},
		})
		return
	}

	// 验证必需字段
	if anthropicRequest.Model == "" {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "model is required",
			},
		})
		return
	}

	if len(anthropicRequest.Messages) == 0 {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "messages is required",
			},
		})
		return
	}

	if anthropicRequest.MaxTokens <= 0 {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "invalid_request_error",
				"message": "max_tokens must be greater than 0",
			},
		})
		return
	}

	// 获取请求ID
	requestID := middleware.GetRequestIDFromContext(c)

	// 处理流式请求
	if anthropicRequest.Stream {
		h.handleAnthropicStreamingRequest(c, &anthropicRequest, requestID, userID, apiKeyID)
		return
	}

	// 处理非流式请求
	response, err := h.processAnthropicRequest(c.Request.Context(), &anthropicRequest, userID, apiKeyID, requestID)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"user_id":    userID,
			"api_key_id": apiKeyID,
			"model":      anthropicRequest.Model,
			"request_id": requestID,
			"error":      err.Error(),
		}).Error("Failed to process Anthropic request")

		c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"type": "error",
			"error": map[string]interface{}{
				"type":    "api_error",
				"message": "Failed to process request",
			},
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// convertToClaudeResponse 将通用AI响应转换为Claude格式
func (h *AIHandler) convertToClaudeResponse(response *clients.AIResponse) *clients.ClaudeMessageResponse {
	claudeResponse := &clients.ClaudeMessageResponse{
		ID:    response.ID,
		Type:  "message",
		Role:  "assistant",
		Model: response.Model,
		Usage: clients.ClaudeUsage{
			InputTokens:  response.Usage.PromptTokens,
			OutputTokens: response.Usage.CompletionTokens,
		},
	}

	// 处理错误
	if response.Error != nil {
		claudeResponse.Error = response.Error
		return claudeResponse
	}

	// 转换内容
	var content []clients.ClaudeContent
	for _, choice := range response.Choices {
		if choice.Message.Content != "" {
			content = append(content, clients.ClaudeContent{
				Type: "text",
				Text: choice.Message.Content,
			})
		}

		// 处理工具调用
		for _, toolCall := range choice.Message.ToolCalls {
			content = append(content, clients.ClaudeContent{
				Type:    "tool_use",
				ID:      toolCall.ID,
				Name:    toolCall.Function.Name,
				Input:   toolCall.Function.Arguments,
				ToolUse: &toolCall,
			})
		}

		// 设置停止原因
		switch choice.FinishReason {
		case "stop":
			claudeResponse.StopReason = "end_turn"
		case "length":
			claudeResponse.StopReason = "max_tokens"
		case "tool_calls":
			claudeResponse.StopReason = "tool_use"
		default:
			claudeResponse.StopReason = "end_turn"
		}
	}

	claudeResponse.Content = content
	return claudeResponse
}

// convertToAnthropicResponse 将通用AI响应转换为Anthropic Messages API格式
func (h *AIHandler) convertToAnthropicResponse(response *clients.AIResponse) *clients.AnthropicMessageResponse {
	anthropicResponse := &clients.AnthropicMessageResponse{
		ID:    response.ID,
		Type:  "message",
		Role:  "assistant",
		Model: response.Model,
		Usage: clients.AnthropicUsage{
			InputTokens:  response.Usage.PromptTokens,
			OutputTokens: response.Usage.CompletionTokens,
		},
	}

	// 转换内容
	var content []clients.AnthropicContentBlock
	for _, choice := range response.Choices {
		if choice.Message.Content != "" {
			content = append(content, clients.AnthropicContentBlock{
				Type: "text",
				Text: choice.Message.Content,
			})
		}

		// 处理工具调用
		for _, toolCall := range choice.Message.ToolCalls {
			content = append(content, clients.AnthropicContentBlock{
				Type:  "tool_use",
				ID:    toolCall.ID,
				Name:  toolCall.Function.Name,
				Input: toolCall.Function.Arguments,
			})
		}

		// 设置停止原因
		switch choice.FinishReason {
		case "stop":
			anthropicResponse.StopReason = "end_turn"
		case "length":
			anthropicResponse.StopReason = "max_tokens"
		case "tool_calls":
			anthropicResponse.StopReason = "tool_use"
		default:
			anthropicResponse.StopReason = "end_turn"
		}
	}

	anthropicResponse.Content = content
	return anthropicResponse
}

// handleClaudeStreamingRequest 处理Claude流式请求
func (h *AIHandler) handleClaudeStreamingRequest(c *gin.Context, gatewayRequest *gateway.GatewayRequest, requestID string, userID, apiKeyID int64) {
	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Request-ID", requestID)

	// 获取响应写入器
	w := c.Writer

	// 检查是否启用了Function Call
	if h.config.FunctionCall.Enabled && len(gatewayRequest.Request.Tools) > 0 {
		// 对于有工具的流式请求，暂时使用非流式处理
		// TODO: 实现Claude格式的Function Call流式处理
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
		}).Warn("Claude streaming with function calls not fully implemented, falling back to non-streaming")

		// 临时处理：转为非流式请求
		gatewayRequest.Request.Stream = false
		ctx := context.Background()
		response, err := h.gatewayService.ProcessRequest(ctx, gatewayRequest)
		if err != nil {
			errorEvent := map[string]interface{}{
				"error": map[string]interface{}{
					"type":    "api_error",
					"message": "Failed to process request",
				},
			}
			errorJSON, _ := json.Marshal(errorEvent)
			w.Write([]byte(fmt.Sprintf("data: %s\n\n", errorJSON)))
			return
		}

		claudeResponse := h.convertToClaudeResponse(response.Response)
		responseJSON, _ := json.Marshal(claudeResponse)
		w.Write([]byte(fmt.Sprintf("data: %s\n\n", responseJSON)))
		return
	}

	// 创建流式响应通道
	streamChan := make(chan *gateway.StreamChunk, 100)
	errorChan := make(chan error, 1)

	// 启动流式处理
	go func() {
		defer func() {
			// 安全关闭channels
			select {
			case <-streamChan:
			default:
				close(streamChan)
			}

			select {
			case <-errorChan:
			default:
				close(errorChan)
			}
		}()

		err := h.gatewayService.ProcessStreamRequest(c.Request.Context(), gatewayRequest, streamChan)
		if err != nil {
			select {
			case errorChan <- err:
			case <-c.Request.Context().Done():
				// 如果上下文已取消，不发送错误
			}
		}
	}()

	// 发送流式数据
	var totalTokens int
	flusher, ok := w.(http.Flusher)
	if !ok {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
		}).Error("Streaming unsupported")
		return
	}

	for {
		select {
		case chunk, ok := <-streamChan:
			if !ok {
				// 流已结束
				return
			}

			// 转换为Claude格式
			claudeChunk := h.convertToClaudeStreamChunk(chunk)
			chunkJSON, err := json.Marshal(claudeChunk)
			if err != nil {
				h.logger.WithFields(map[string]interface{}{
					"request_id": requestID,
					"error":      err.Error(),
				}).Error("Failed to marshal Claude stream chunk")
				continue
			}

			w.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkJSON)))
			flusher.Flush()

			if chunk.Usage != nil {
				totalTokens = chunk.Usage.TotalTokens
			}

		case err := <-errorChan:
			h.logger.WithFields(map[string]interface{}{
				"user_id":    userID,
				"api_key_id": apiKeyID,
				"model":      gatewayRequest.Request.Model,
				"request_id": requestID,
				"error":      err.Error(),
			}).Error("Failed to process Claude stream request")

			// 发送错误事件
			errorEvent := map[string]interface{}{
				"error": map[string]interface{}{
					"type":    "api_error",
					"message": "Failed to process request",
				},
			}
			errorJSON, _ := json.Marshal(errorEvent)
			w.Write([]byte(fmt.Sprintf("data: %s\n\n", errorJSON)))
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			h.logger.WithFields(map[string]interface{}{
				"request_id":   requestID,
				"total_tokens": totalTokens,
			}).Info("Claude stream request cancelled by client")
			return
		}
	}
}

// convertToClaudeStreamChunk 将流式响应块转换为Claude格式
func (h *AIHandler) convertToClaudeStreamChunk(chunk *gateway.StreamChunk) map[string]interface{} {
	claudeChunk := map[string]interface{}{
		"type":  "content_block_delta",
		"index": 0,
		"delta": map[string]interface{}{
			"type": "text_delta",
			"text": chunk.Content,
		},
	}

	// 如果是最后一个块，添加完成信息
	if chunk.FinishReason != nil {
		claudeChunk["type"] = "message_delta"
		claudeChunk["delta"] = map[string]interface{}{
			"stop_reason": h.convertFinishReasonToClaude(*chunk.FinishReason),
		}

		// 添加使用情况信息
		if chunk.Usage != nil {
			claudeChunk["usage"] = map[string]interface{}{
				"input_tokens":  chunk.Usage.PromptTokens,
				"output_tokens": chunk.Usage.CompletionTokens,
			}
		}
	}

	return claudeChunk
}

// convertFinishReasonToClaude 转换完成原因为Claude格式
func (h *AIHandler) convertFinishReasonToClaude(finishReason string) string {
	switch finishReason {
	case "stop":
		return "end_turn"
	case "length":
		return "max_tokens"
	case "tool_calls":
		return "tool_use"
	default:
		return "end_turn"
	}
}

// extractSystemContent 从system字段中提取文本内容
func (h *AIHandler) extractSystemContent(system interface{}) string {
	if system == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := system.(string); ok {
		return str
	}

	// 如果是数组格式，提取text内容
	if arr, ok := system.([]interface{}); ok {
		for _, item := range arr {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if itemType, exists := itemMap["type"]; exists && itemType == "text" {
					if text, exists := itemMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							return textStr
						}
					}
				}
			}
		}
	}

	// 如果是单个对象格式
	if objMap, ok := system.(map[string]interface{}); ok {
		if objType, exists := objMap["type"]; exists && objType == "text" {
			if text, exists := objMap["text"]; exists {
				if textStr, ok := text.(string); ok {
					return textStr
				}
			}
		}
	}

	return ""
}

// processAnthropicRequest 处理Anthropic非流式请求
func (h *AIHandler) processAnthropicRequest(ctx context.Context, request *clients.AnthropicMessageRequest, userID, apiKeyID int64, requestID string) (*clients.AnthropicMessageResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"request_id": requestID,
		"user_id":    userID,
		"api_key_id": apiKeyID,
		"model":      request.Model,
		"max_tokens": request.MaxTokens,
		"stream":     request.Stream,
	}).Info("开始处理 Anthropic 请求")

	// 从数据库获取支持该模型的提供商
	supportInfos, err := h.providerModelSupportRepo.GetSupportingProviders(ctx, request.Model)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"model":      request.Model,
			"error":      err.Error(),
		}).Error("获取支持该模型的提供商失败")
		return nil, fmt.Errorf("failed to get supporting providers: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"request_id":      requestID,
		"model":           request.Model,
		"providers_count": len(supportInfos),
	}).Info("从数据库获取到支持的提供商")

	if len(supportInfos) == 0 {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"model":      request.Model,
		}).Error("没有提供商支持该模型")
		return nil, fmt.Errorf("no providers support model: %s", request.Model)
	}

	// 选择第一个可用的提供商（可以后续优化为负载均衡）
	var selectedProvider *entities.Provider
	var selectedModelInfo *entities.ModelSupportInfo
	for i, info := range supportInfos {
		h.logger.WithFields(map[string]interface{}{
			"request_id":    requestID,
			"provider_id":   info.Provider.ID,
			"provider_name": info.Provider.Name,
			"provider_slug": info.Provider.Slug,
			"base_url":      info.Provider.BaseURL,
			"status":        info.Provider.Status,
			"priority":      info.Provider.Priority,
			"index":         i,
		}).Info("检查提供商可用性")

		if info.Provider.IsAvailable() {
			selectedProvider = info.Provider
			selectedModelInfo = info
			h.logger.WithFields(map[string]interface{}{
				"request_id":    requestID,
				"provider_id":   info.Provider.ID,
				"provider_name": info.Provider.Name,
				"provider_slug": info.Provider.Slug,
				"base_url":      info.Provider.BaseURL,
			}).Info("选择了可用的提供商")
			break
		} else {
			h.logger.WithFields(map[string]interface{}{
				"request_id":    requestID,
				"provider_id":   info.Provider.ID,
				"provider_name": info.Provider.Name,
				"status":        info.Provider.Status,
			}).Warn("提供商不可用，跳过")
		}
	}

	if selectedProvider == nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"model":      request.Model,
		}).Error("没有可用的提供商")
		return nil, fmt.Errorf("no available providers for model: %s", request.Model)
	}

	// 直接发送Anthropic格式请求到提供商
	h.logger.WithFields(map[string]interface{}{
		"request_id":          requestID,
		"provider_id":         selectedProvider.ID,
		"provider_name":       selectedProvider.Name,
		"provider_slug":       selectedProvider.Slug,
		"base_url":            selectedProvider.BaseURL,
		"upstream_model_name": selectedModelInfo.UpstreamModelName,
	}).Info("开始发送请求到上游提供商")

	response, err := h.sendAnthropicRequestToProvider(ctx, selectedProvider, selectedModelInfo, request)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"request_id":    requestID,
			"provider_id":   selectedProvider.ID,
			"provider_name": selectedProvider.Name,
			"base_url":      selectedProvider.BaseURL,
			"error":         err.Error(),
		}).Error("发送请求到提供商失败")
		return nil, fmt.Errorf("failed to send request to provider: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"request_id":    requestID,
		"provider_id":   selectedProvider.ID,
		"provider_name": selectedProvider.Name,
		"response_id":   response.ID,
		"input_tokens":  response.Usage.InputTokens,
		"output_tokens": response.Usage.OutputTokens,
	}).Info("成功收到提供商响应")

	return response, nil
}

// sendAnthropicRequestToProvider 发送Anthropic请求到提供商
func (h *AIHandler) sendAnthropicRequestToProvider(ctx context.Context, provider *entities.Provider, modelInfo *entities.ModelSupportInfo, request *clients.AnthropicMessageRequest) (*clients.AnthropicMessageResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"provider_id":         provider.ID,
		"provider_name":       provider.Name,
		"provider_slug":       provider.Slug,
		"base_url":            provider.BaseURL,
		"original_model":      request.Model,
		"upstream_model_name": modelInfo.UpstreamModelName,
		"max_tokens":          request.MaxTokens,
		"stream":              request.Stream,
	}).Info("开始直接发送 Anthropic 格式请求")

	// 构造请求URL - 强制使用 /messages 端点
	baseURL := strings.TrimSuffix(provider.BaseURL, "/")
	var url string
	if strings.HasSuffix(baseURL, "/v1") {
		url = fmt.Sprintf("%s/messages", baseURL)
	} else {
		url = fmt.Sprintf("%s/v1/messages", baseURL)
	}

	// 构造请求头
	headers := map[string]string{
		"Content-Type":      "application/json",
		"anthropic-version": "2023-06-01",
	}

	// 设置认证头
	if provider.APIKeyEncrypted != nil {
		headers["x-api-key"] = *provider.APIKeyEncrypted
	}

	// 使用上游模型名称
	requestCopy := *request
	if modelInfo.UpstreamModelName != "" {
		requestCopy.Model = modelInfo.UpstreamModelName
	}

	h.logger.WithFields(map[string]interface{}{
		"provider_id":   provider.ID,
		"provider_name": provider.Name,
		"url":           url,
		"final_model":   requestCopy.Model,
		"headers":       headers,
	}).Info("准备发送 HTTP 请求到 /v1/messages 端点")

	// 直接发送HTTP请求
	httpResponse, err := h.httpClient.Post(ctx, url, &requestCopy, headers)
	if err != nil {
		h.logger.WithFields(map[string]interface{}{
			"provider_id":   provider.ID,
			"provider_name": provider.Name,
			"url":           url,
			"error":         err.Error(),
		}).Error("HTTP 请求失败")
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"provider_id":    provider.ID,
		"provider_name":  provider.Name,
		"url":            url,
		"status_code":    httpResponse.StatusCode,
		"content_length": len(httpResponse.Body),
	}).Info("收到 HTTP 响应")

	// 检查响应状态
	if httpResponse.StatusCode != 200 {
		h.logger.WithFields(map[string]interface{}{
			"provider_id":   provider.ID,
			"provider_name": provider.Name,
			"url":           url,
			"status_code":   httpResponse.StatusCode,
			"response_body": string(httpResponse.Body),
		}).Error("提供商返回错误状态码")
		return nil, fmt.Errorf("provider returned status %d: %s", httpResponse.StatusCode, string(httpResponse.Body))
	}

	// 解析响应
	var anthropicResponse clients.AnthropicMessageResponse
	if err := json.Unmarshal(httpResponse.Body, &anthropicResponse); err != nil {
		h.logger.WithFields(map[string]interface{}{
			"provider_id":   provider.ID,
			"provider_name": provider.Name,
			"url":           url,
			"error":         err.Error(),
			"response_body": string(httpResponse.Body),
		}).Error("解析响应失败")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"provider_id":    provider.ID,
		"provider_name":  provider.Name,
		"url":            url,
		"response_id":    anthropicResponse.ID,
		"response_model": anthropicResponse.Model,
		"content_blocks": len(anthropicResponse.Content),
		"stop_reason":    anthropicResponse.StopReason,
		"input_tokens":   anthropicResponse.Usage.InputTokens,
		"output_tokens":  anthropicResponse.Usage.OutputTokens,
	}).Info("Anthropic 响应解析成功")

	return &anthropicResponse, nil
}

// handleAnthropicStreamingRequest 处理Anthropic流式请求
func (h *AIHandler) handleAnthropicStreamingRequest(c *gin.Context, request *clients.AnthropicMessageRequest, requestID string, userID, apiKeyID int64) {
	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Request-ID", requestID)

	// 获取响应写入器
	w := c.Writer
	flusher, ok := w.(http.Flusher)
	if !ok {
		h.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
		}).Error("Streaming unsupported")
		return
	}

	// 从数据库获取支持该模型的提供商
	supportInfos, err := h.providerModelSupportRepo.GetSupportingProviders(c.Request.Context(), request.Model)
	if err != nil {
		h.sendStreamError(w, flusher, "Failed to get supporting providers", err)
		return
	}

	if len(supportInfos) == 0 {
		h.sendStreamError(w, flusher, "No providers support model: "+request.Model, nil)
		return
	}

	// 选择第一个可用的提供商
	var selectedProvider *entities.Provider
	var selectedModelInfo *entities.ModelSupportInfo
	for _, info := range supportInfos {
		if info.Provider.IsAvailable() {
			selectedProvider = info.Provider
			selectedModelInfo = info
			break
		}
	}

	if selectedProvider == nil {
		h.sendStreamError(w, flusher, "No available providers for model: "+request.Model, nil)
		return
	}

	// 直接发送流式请求到 /v1/messages 端点
	err = h.sendAnthropicStreamRequestToProvider(c, selectedProvider, selectedModelInfo, request, w, flusher)
	if err != nil {
		h.sendStreamError(w, flusher, "Failed to process stream request", err)
		return
	}
}

// convertToAnthropicStreamChunk 将流式响应块转换为Anthropic格式
func (h *AIHandler) convertToAnthropicStreamChunk(chunk *gateway.StreamChunk) map[string]interface{} {
	// Anthropic 流式响应格式
	anthropicChunk := map[string]interface{}{
		"type":  "content_block_delta",
		"index": 0,
		"delta": map[string]interface{}{
			"type": "text_delta",
			"text": chunk.Content,
		},
	}

	// 如果是最后一个块，添加完成信息
	if chunk.FinishReason != nil {
		anthropicChunk["type"] = "message_delta"
		anthropicChunk["delta"] = map[string]interface{}{
			"stop_reason": h.convertFinishReasonToAnthropic(*chunk.FinishReason),
		}

		// 添加使用情况信息
		if chunk.Usage != nil {
			anthropicChunk["usage"] = map[string]interface{}{
				"input_tokens":  chunk.Usage.PromptTokens,
				"output_tokens": chunk.Usage.CompletionTokens,
			}
		}
	}

	return anthropicChunk
}

// convertFinishReasonToAnthropic 转换完成原因为Anthropic格式
func (h *AIHandler) convertFinishReasonToAnthropic(finishReason string) string {
	switch finishReason {
	case "stop":
		return "end_turn"
	case "length":
		return "max_tokens"
	case "tool_calls":
		return "tool_use"
	default:
		return "end_turn"
	}
}

// sendStreamError 发送流式错误响应
func (h *AIHandler) sendStreamError(w http.ResponseWriter, flusher http.Flusher, message string, err error) {
	errorEvent := map[string]interface{}{
		"type": "error",
		"error": map[string]interface{}{
			"type":    "api_error",
			"message": message,
		},
	}

	if err != nil {
		errorEvent["error"].(map[string]interface{})["details"] = err.Error()
	}

	errorJSON, _ := json.Marshal(errorEvent)
	w.Write([]byte(fmt.Sprintf("data: %s\n\n", errorJSON)))
	flusher.Flush()
}

// sendAnthropicStreamRequestToProvider 发送Anthropic流式请求到提供商
func (h *AIHandler) sendAnthropicStreamRequestToProvider(c *gin.Context, provider *entities.Provider, modelInfo *entities.ModelSupportInfo, request *clients.AnthropicMessageRequest, w http.ResponseWriter, flusher http.Flusher) error {
	// 构造请求URL - 强制使用 /messages 端点
	baseURL := strings.TrimSuffix(provider.BaseURL, "/")
	var url string
	if strings.HasSuffix(baseURL, "/v1") {
		url = fmt.Sprintf("%s/messages", baseURL)
	} else {
		url = fmt.Sprintf("%s/v1/messages", baseURL)
	}

	h.logger.WithFields(map[string]interface{}{
		"provider_id":         provider.ID,
		"provider_name":       provider.Name,
		"provider_slug":       provider.Slug,
		"base_url":            provider.BaseURL,
		"stream_url":          url,
		"original_model":      request.Model,
		"upstream_model_name": modelInfo.UpstreamModelName,
	}).Info("开始发送流式请求到 /v1/messages 端点")

	// 构造请求头
	headers := map[string]string{
		"Content-Type":      "application/json",
		"anthropic-version": "2023-06-01",
		"Accept":            "text/event-stream",
	}

	// 设置认证
	if provider.APIKeyEncrypted != nil {
		headers["x-api-key"] = *provider.APIKeyEncrypted
	}

	// 使用上游模型名称
	requestCopy := *request
	if modelInfo.UpstreamModelName != "" {
		requestCopy.Model = modelInfo.UpstreamModelName
	}

	// 确保是流式请求
	requestCopy.Stream = true

	h.logger.WithFields(map[string]interface{}{
		"provider_id": provider.ID,
		"stream_url":  url,
		"final_model": requestCopy.Model,
		"stream":      requestCopy.Stream,
		"headers":     headers,
	}).Info("准备发送流式 HTTP 请求")

	// 序列化请求体
	requestBody, err := json.Marshal(&requestCopy)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(c.Request.Context(), "POST", url, strings.NewReader(string(requestBody)))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		httpReq.Header.Set(key, value)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 处理流式响应
	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()

		// 处理 event: 行
		if strings.HasPrefix(line, "event: ") {
			w.Write([]byte(line + "\n"))
			flusher.Flush()
		} else if strings.HasPrefix(line, "data: ") {
			data := line[6:] // 移除 "data: " 前缀
			if data == "[DONE]" {
				w.Write([]byte("data: [DONE]\n\n"))
				flusher.Flush()
				break
			}

			// 直接转发Anthropic格式的数据
			w.Write([]byte(fmt.Sprintf("data: %s\n", data)))
			flusher.Flush()
		} else if line == "" {
			// 处理空行（SSE 事件分隔符）
			w.Write([]byte("\n"))
			flusher.Flush()
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading stream: %w", err)
	}

	return nil
}

// convertAnthropicToAIRequest 将 Anthropic 请求转换为通用 AI 请求
func (h *AIHandler) convertAnthropicToAIRequest(request *clients.AnthropicMessageRequest, modelInfo *entities.ModelSupportInfo) *clients.AIRequest {
	// 转换消息格式
	var aiMessages []clients.AIMessage
	for _, anthropicMsg := range request.Messages {
		aiMessages = append(aiMessages, clients.AIMessage{
			Role:    anthropicMsg.Role,
			Content: anthropicMsg.GetTextContent(),
		})
	}

	// 转换工具格式
	var tools []clients.Tool
	for _, anthropicTool := range request.Tools {
		tools = append(tools, clients.Tool{
			Type: "function",
			Function: clients.Function{
				Name:        anthropicTool.Name,
				Description: anthropicTool.Description,
				Parameters:  anthropicTool.InputSchema,
			},
		})
	}

	// 设置温度默认值
	temperature := 1.0
	if request.Temperature != nil {
		temperature = *request.Temperature
	}

	// 使用上游模型名称
	model := request.Model
	if modelInfo.UpstreamModelName != "" {
		model = modelInfo.UpstreamModelName
	}

	return &clients.AIRequest{
		Model:       model,
		Messages:    aiMessages,
		MaxTokens:   request.MaxTokens,
		Temperature: temperature,
		Stream:      request.Stream,
		Tools:       tools,
		ToolChoice:  request.ToolChoice,
		Extra: map[string]interface{}{
			"system":         request.System,
			"stop_sequences": request.StopSequences,
			"top_k":          request.TopK,
			"top_p":          request.TopP,
			"service_tier":   request.ServiceTier,
			"metadata":       request.Metadata,
			"container":      request.Container,
			"mcp_servers":    request.MCPServers,
			"thinking":       request.Thinking,
		},
	}
}

// convertAIResponseToAnthropic 将通用 AI 响应转换为 Anthropic 格式
func (h *AIHandler) convertAIResponseToAnthropic(response *clients.AIResponse) *clients.AnthropicMessageResponse {
	anthropicResponse := &clients.AnthropicMessageResponse{
		ID:    response.ID,
		Type:  "message",
		Role:  "assistant",
		Model: response.Model,
		Usage: clients.AnthropicUsage{
			InputTokens:  response.Usage.PromptTokens,
			OutputTokens: response.Usage.CompletionTokens,
		},
	}

	// 转换内容
	var content []clients.AnthropicContentBlock
	for _, choice := range response.Choices {
		if choice.Message.Content != "" {
			content = append(content, clients.AnthropicContentBlock{
				Type: "text",
				Text: choice.Message.Content,
			})
		}

		// 处理工具调用
		for _, toolCall := range choice.Message.ToolCalls {
			content = append(content, clients.AnthropicContentBlock{
				Type:  "tool_use",
				ID:    toolCall.ID,
				Name:  toolCall.Function.Name,
				Input: toolCall.Function.Arguments,
			})
		}

		// 设置停止原因
		switch choice.FinishReason {
		case "stop":
			anthropicResponse.StopReason = "end_turn"
		case "length":
			anthropicResponse.StopReason = "max_tokens"
		case "tool_calls":
			anthropicResponse.StopReason = "tool_use"
		default:
			anthropicResponse.StopReason = "end_turn"
		}
	}

	anthropicResponse.Content = content
	return anthropicResponse
}

// convertStreamChunkToAnthropic 将流式数据块转换为Anthropic格式
func (h *AIHandler) convertStreamChunkToAnthropic(chunk *clients.StreamChunk) map[string]interface{} {
	// Anthropic 流式响应格式
	anthropicChunk := map[string]interface{}{
		"type":  "content_block_delta",
		"index": 0,
		"delta": map[string]interface{}{
			"type": "text_delta",
			"text": chunk.Content,
		},
	}

	// 如果是最后一个块，添加完成信息
	if chunk.FinishReason != nil {
		anthropicChunk["type"] = "message_delta"
		anthropicChunk["delta"] = map[string]interface{}{
			"stop_reason": h.convertFinishReasonToAnthropic(*chunk.FinishReason),
		}

		// 添加使用情况信息
		if chunk.Usage != nil {
			anthropicChunk["usage"] = map[string]interface{}{
				"input_tokens":  chunk.Usage.PromptTokens,
				"output_tokens": chunk.Usage.CompletionTokens,
			}
		}
	}

	return anthropicChunk
}
