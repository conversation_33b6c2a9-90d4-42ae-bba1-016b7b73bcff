server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

database:
  # 数据库驱动: sqlite 或 postgres
  driver: "postgres"
  # PostgreSQL连接字符串
  # dsn: "host=************* port=35432 user=user_4cWfeb password=MKwiCfe4RipTK63t dbname=ai sslmode=disable"
  host: *************
  port: 5432
  user: ai718
  password: BUX3qwLtyF6B3f0UbcY
  dbname: ai718
  sslmode: disable
  # 连接池配置
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

logging:
  level: "info"
  format: "text"
  output: "stdout"

rate_limiting:
  default_requests_per_minute: 60
  default_requests_per_hour: 1000
  default_requests_per_day: 10000

load_balancer:
  strategy: "round_robin" # round_robin, weighted, least_connections
  health_check_enabled: true
  failover_enabled: true

monitoring:
  health_check_path: "/health"

billing:
  currency: "USD"
  precision: 6 # decimal places for cost calculations
  batch_size: 100 # batch size for billing record processing

# Redis配置

redis:
  # Redis服务器地址
  addr: "*************:6379"
  # Redis密码（如果有的话）
  password: "QmpyOuWdwAHE"
  # Redis数据库编号
  db: 5
  # 连接池大小
  pool_size: 10
  # 最小空闲连接数
  min_idle_conns: 5
  # 连接超时时间
  dial_timeout: "5s"
  # 读取超时时间
  read_timeout: "3s"
  # 写入超时时间
  write_timeout: "3s"
  # 连接池超时时间
  pool_timeout: "4s"
  # 空闲连接检查频率
  idle_check_frequency: "60s"
  # 空闲连接超时时间
  idle_timeout: "5m"
  # 最大连接存活时间
  max_conn_age: "30m"

# 缓存配置
cache:
  # 是否启用缓存
  enabled: true

  # 基础缓存TTL配置
  default_ttl: "5m"

  # 实体缓存TTL
  entity:
    user_ttl: "10m"              # 用户信息缓存时间
    api_key_ttl: "15m"           # API密钥缓存时间
    model_ttl: "30m"             # 模型信息缓存时间
    provider_ttl: "30m"          # 提供商信息缓存时间
    quota_ttl: "1m"              # 配额信息缓存时间

  # 查询缓存TTL
  query:
    user_lookup_ttl: "5m"        # 用户查询（按用户名/邮箱）缓存时间
    model_list_ttl: "30m"        # 模型列表缓存时间
    provider_list_ttl: "30m"     # 提供商列表缓存时间
    quota_usage_ttl: "2m"        # 配额使用情况缓存时间
    user_quota_list_ttl: "5m"    # 用户配额列表缓存时间
    api_key_list_ttl: "10m"      # API密钥列表缓存时间
    usage_log_ttl: "10m"         # 使用日志查询缓存时间

  # 统计缓存TTL
  stats:
    count_ttl: "10m"             # 计数统计缓存时间
    pagination_ttl: "5m"         # 分页列表缓存时间

  # 缓存功能开关
  features:
    entity_cache: true           # 实体缓存
    list_cache: true             # 列表缓存
    query_cache: true            # 复合查询缓存
    stats_cache: true            # 统计缓存
    auto_invalidation: true      # 自动缓存失效

  # 缓存性能配置
  performance:
    batch_invalidation: true     # 批量失效
    preload_on_startup: true     # 启动时预加载
    max_key_length: 250          # 最大键长度

  # 向后兼容配置（保持原有配置键）
  user_ttl: "10m"
  api_key_ttl: "15m"
  model_ttl: "30m"
  provider_ttl: "30m"
  quota_ttl: "1m"

# 异步配额消费者配置
async_quota:
  # 是否启用异步配额处理
  enabled: true

  # 消费者配置
  consumer:
    worker_count: 3              # 工作协程数量
    channel_size: 1000           # 通道缓冲区大小
    batch_size: 10               # 批量处理大小
    flush_interval: "5s"         # 强制刷新间隔
    retry_attempts: 3            # 重试次数
    retry_delay: "100ms"         # 重试延迟

  # 性能配置
  performance:
    enable_batching: true        # 启用批量处理
    enable_compression: false    # 启用事件压缩（暂未实现）
    max_memory_usage: "100MB"    # 最大内存使用（暂未实现）



# OAuth认证配置
oauth:
  frontend_url: "http://dash-dev.718ai.cn"
  google:
    enabled: true
    client_id: "your-google-oauth-client-id"
    client_secret: "your-google-oauth-client-secret"
    redirect_url: "https://api-dev.718ai.cn/auth/oauth/google/callback"
  github:
    enabled: true
    client_id: "your-github-oauth-client-id"
    client_secret: "your-github-oauth-client-secret"
    redirect_url: "https://api-dev.718ai.cn/auth/oauth/github/callback"



# JWT认证配置
jwt:
  # JWT密钥（生产环境请使用更复杂的密钥）
  secret: "your-super-secret-jwt-key-change-this-in-production"
  # Token过期时间
  access_token_ttl: "24h"
  # 刷新Token过期时间
  refresh_token_ttl: "168h" # 7天
  # 签发者
  issuer: "ai-api-gateway"
  # 受众
  audience: "ai-api-gateway-users"

# 分布式锁配置
distributed_lock:
  # 是否启用分布式锁
  enabled: true
  # 锁的默认过期时间
  default_ttl: "30s"
  # 获取锁的重试间隔
  retry_interval: "100ms"
  # 获取锁的最大重试次数
  max_retries: 10

# Function Call 配置
function_call:
  # 是否启用 Function Call 功能
  enabled: true

  # 搜索服务配置
  search_service:
    # 搜索服务类型: search1api, google, bing, serpapi, serper, duckduckgo, searxng
    service: "google"
    # 最大搜索结果数
    max_results: 5
    # 深度搜索数量（仅 search1api 支持）
    crawl_results: 0
    # 是否爬取网页内容并转换为Markdown（启用后会显示数据源）
    crawl_content: true

    # 各搜索服务的 API 密钥（根据使用的服务配置）
    # search1api_key: "your_search1api_key"
    google_cx: "05afc7eed6abd4a3c"
    google_key: "AIzaSyAJ-0mmqqaR610601edOxYw4MsS6GoavcY"
    # bing_key: "your_bing_search_api_key"
    # serpapi_key: "your_serpapi_key"
    # serper_key: "your_serper_key"
    # searxng_base_url: "https://your-searxng-instance.com"

# 邮件服务配置
email:
  # 邮件服务提供商: brevo
  provider: "brevo"

  # Brevo配置
  brevo:
    # Brevo API密钥
    api_key: "your-brevo-api-key"
    # API基础URL
    api_url: "https://api.brevo.com/v3"
    # 发送者信息
    sender:
      email: "<EMAIL>"
      name: "718AI"
    # 邮件发送超时时间
    timeout: "30s"
    # 重试配置
    retry:
      max_attempts: 3
      delay: "1s"

# 验证码配置
verification:
  # 验证码长度
  code_length: 6
  # 验证码过期时间
  expire_time: "10m"
  # 验证码类型: numeric, alphanumeric
  code_type: "numeric"
  # 同一邮箱发送间隔（防止频繁发送）
  send_interval: "1m"
  # 验证码最大尝试次数
  max_attempts: 5

# S3存储配置
s3:
  # 是否启用S3存储
  enabled: false
  # AWS区域
  region: "us-east-1"
  # S3存储桶名称
  bucket: "your-s3-bucket-name"
  # AWS访问密钥ID
  access_key_id: "your-aws-access-key-id"
  # AWS秘密访问密钥
  secret_access_key: "your-aws-secret-access-key"
  # 自定义端点（用于兼容S3的服务，如MinIO）
  endpoint: ""
  # 是否使用路径样式URL（MinIO等服务需要设置为true）
  use_path_style: false
  # 最大文件大小（字节，默认10MB）
  max_file_size: 10485760
  # 允许的文件类型
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"