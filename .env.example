# AI API Gateway Environment Variables

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration
DATABASE_DRIVER=sqlite3
DATABASE_DSN=./data/gateway.db

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Provider API Keys (Keep these secure!)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here
API_KEY_SALT=your_api_key_salt_here

# OAuth Configuration
# Google OAuth
OAUTH_GOOGLE_ENABLED=false
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id_here
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret_here
OAUTH_GOOGLE_REDIRECT_URL=http://localhost:8080/auth/oauth/google/callback

# GitHub OAuth
OAUTH_GITHUB_ENABLED=false
OAUTH_GITHUB_CLIENT_ID=your_github_client_id_here
OAUTH_GITHUB_CLIENT_SECRET=your_github_client_secret_here
OAUTH_GITHUB_REDIRECT_URL=http://localhost:8080/auth/oauth/github/callback

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090

# Development/Production Mode
GIN_MODE=release
