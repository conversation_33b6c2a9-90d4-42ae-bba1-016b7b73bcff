import 'src/global.css';

import { useEffect } from 'react';

import { usePathname } from 'src/routes/hooks';

import TokenStorage from 'src/utils/token-storage';

import AuthService from 'src/services/auth';
import { ThemeProvider } from 'src/theme/theme-provider';
import { AuthProvider, useAuth } from 'src/contexts/auth-context';

// ----------------------------------------------------------------------

type AppProps = {
  children: React.ReactNode;
};

export default function App({ children }: AppProps) {
  useScrollToTop();
  useOAuthTokenHandler();

  return (
    <ThemeProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </ThemeProvider>
  );
}

// ----------------------------------------------------------------------

function useScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

function useOAuthTokenHandler() {
  useEffect(() => {
    const handleOAuthTokens = async () => {
      // 检查URL参数中是否有OAuth token
      const urlParams = new URLSearchParams(window.location.search);
      const accessToken = urlParams.get('access_token');
      const refreshToken = urlParams.get('refresh_token');

      if (accessToken && refreshToken) {
        console.log('🔑 OAuth tokens detected, processing...');

        try {
          // 存储token
          console.log('💾 Storing tokens...');
          TokenStorage.setAccessToken(accessToken);
          TokenStorage.setRefreshToken(refreshToken);

          // 使用项目的AuthService获取用户信息
          console.log('👤 Fetching user profile...');
          const userInfo = await AuthService.getProfile();
          console.log('✅ User profile fetched:', userInfo);

          // 检查用户信息是否完整
          if (!userInfo || !userInfo.id || !userInfo.email) {
            console.error('❌ Incomplete user info:', userInfo);
            throw new Error('Incomplete user information received');
          }

          // 存储用户信息
          TokenStorage.setUserInfo(userInfo);

          // 清除URL参数
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);

          console.log('🎉 OAuth login completed successfully!');

          // 设置OAuth登录标志，防止页面刷新后被重定向
          sessionStorage.setItem('oauth_login_success', 'true');

          // 延迟一下再刷新页面，确保所有数据都已存储
          setTimeout(() => {
            window.location.reload();
          }, 100);

        } catch (error) {
          console.error('❌ Failed to process OAuth tokens:', error);
          // 如果出错，清除可能的无效token
          TokenStorage.clearAuthData();
          // 重定向到登录页
          console.log('🔄 Redirecting to login page due to error');
          window.location.href = '/sign-in';
        }
      }
    };

    handleOAuthTokens();
  }, []);

  return null;
}
