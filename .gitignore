# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
config.yaml
configs/config.yaml

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local

# Database files
*.db
*.sqlite
*.sqlite3
data/

# Logs
*.log
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
dist/
build/
bin/

# Temporary files
tmp/
temp/
node_modules/"configs/config.yaml" 
