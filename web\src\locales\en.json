{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "creating": "Creating...", "update": "Update", "updating": "Updating...", "search": "Search", "filter": "Filter", "clear": "Clear", "copy": "Copy", "copied": "Copied!", "refresh": "Refresh", "close": "Close", "view": "View", "details": "Details", "actions": "Actions", "status": "Status", "name": "Name", "description": "Description", "created_at": "Created At", "updated_at": "Updated At", "page": "Page", "total": "Total", "items": "items", "enable": "Enable", "disable": "Disable", "need_help": "Need help?"}, "profile": {"title": "Profile", "account_settings": "Account <PERSON><PERSON>", "basic_info": "Basic Information", "basic_info_desc": "Your basic account information", "username": "Username", "email": "Email", "password": "Password", "password_desc": "Change your password regularly to keep your account secure", "change_password": "Change Password", "change_password_desc": "Please enter your current password and new password", "current_password": "Current Password", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "current_password_required": "Please enter current password", "new_password_required": "Please enter new password", "confirm_password_required": "Please confirm new password", "password_min_length": "Password must be at least 6 characters", "passwords_not_match": "Passwords do not match", "update_password": "Update Password", "account_status": "Account Status", "status": "Status", "active": "Active", "balance": "Balance", "user_not_found": "User information not found", "please_login_again": "Please login again"}, "wallet": {"title": "Wallet", "current_balance": "Current Balance", "recharge": "Recharge", "account_info": "Account Information", "username": "Username", "email": "Email", "account_status": "Account Status", "active": "Active", "usage_instructions": "Usage Instructions", "instruction_1": "Balance is used to pay for API call fees", "instruction_2": "API calls will be rejected when balance is insufficient", "instruction_3": "Balance will take effect immediately after recharge", "instruction_4": "Contact customer service if you have any questions", "user_not_found": "User information not found", "please_login_again": "Please login again", "recharge_desc": "Please enter the amount to recharge, balance will take effect immediately", "recharge_amount": "Recharge Amount", "quick_amounts": "Quick Amounts", "balance_after_recharge": "Balance After Recharge", "confirm_recharge": "Confirm Recharge", "amount_required": "Please enter recharge amount", "amount_must_positive": "Recharge amount must be greater than 0", "amount_too_large": "Single recharge amount cannot exceed $10,000", "recharge_failed": "Recharge failed, please try again", "recharge_success": "Recharge successful"}, "tools": {"title": "My Tools", "description": "Explore our collection of AI tools to boost your productivity and creativity", "description_new": "Create and manage your AI tools, share them with other users", "create_tool": "Create Tool", "categories": "Categories", "category_all": "All", "category_my tools": "My Tools", "category_public": "Public", "category_shared": "Shared", "features": "Features", "available": "Available", "beta": "Beta", "coming_soon": "Coming Soon", "launch": "Launch Tool", "try_beta": "Try Beta", "no_tools_found": "No tools found", "try_different_category": "Try selecting a different category", "launch_tool": "Launch Tool", "select_api_key": "Select API Key", "select_model": "Select Model", "no_api_keys_available": "No API keys available", "create_api_key": "Create API Key", "api_key": "API Key", "model": "Model", "no_compatible_models": "No compatible models", "model_details": "Model Details", "provider": "Provider", "type": "Type", "estimated_cost": "Estimated Cost", "current_balance": "Current Balance", "insufficient_balance": "Insufficient Balance", "recharge_now": "Recharge Now", "public": "Public", "creator": "Creator", "usage_count": "Usage Count", "created": "Created", "edit": "Edit", "share": "Share", "delete": "Delete", "launch_failed": "Failed to launch tool", "share_success": "Share link copied to clipboard", "share_failed": "Share failed, please try again", "select_tool_type": "Select Tool Type", "tool_configuration": "Tool Configuration", "tool_name": "Tool Name", "tool_description": "Tool Description", "make_public": "Make Public", "tool_preview": "Tool Preview", "untitled_tool": "Untitled Tool", "tool_types": {"chatbot": "AI Chatbot", "image_generator": "Image Generator", "text_generator": "Text Generator", "code_assistant": "Code Assistant"}, "basic_info": "Basic Information", "configuration": "Configuration", "update_tool": "Update Tool"}, "models": {"title": "AI Models", "description": "Description", "name": "Model Name", "provider": "Provider", "type": "Type", "context_length": "Context Length", "rate_multiplier": "Rate Multiplier", "all_providers": "All Providers", "categories": "Categories", "types": "Types", "category_all": "All", "category_premium": "Premium", "category_creative": "Creative", "category_audio": "Audio", "category_experimental": "Experimental", "type_all": "All", "type_text": "Text", "type_chat": "Cha<PERSON>", "type_image": "Image", "type_audio": "Audio", "type_video": "Video", "type_embedding": "Embedding", "type_multimodal": "Multimodal", "pricing": "Pricing", "capabilities": "Capabilities", "max_tokens": "<PERSON>", "view_details": "View Details", "no_models_found": "No models found", "try_different_filter": "Try different filter criteria"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "username": "Username", "username_optional": "Username (Optional)", "username_auto_generate": "Leave blank to auto-generate 718_xxxxxx format username", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "forgot_password": "Forgot Password?", "remember_me": "Remember me", "login_success": "Login successful", "logout_success": "Logout successful", "invalid_credentials": "Invalid credentials", "registration_success": "Registration successful", "no_account": "Don't have an account?", "get_started": "Get started", "have_account": "Already have an account?", "or": "OR", "registration_successful": "Registration Successful!", "account_created": "Your account has been created successfully. You can now sign in with your credentials.", "back_to_signin": "Back to Sign In", "username_required": "Username is required", "username_min_length": "Username must be at least 3 characters", "email_required": "Email is required", "email_invalid": "Please enter a valid email address", "password_required": "Password is required", "password_min_length": "Password must be at least 6 characters", "confirm_password_required": "Please confirm your password", "passwords_not_match": "Passwords do not match", "invalid_credentials_error": "Invalid email or password", "user_already_exists": "User already exists, please use a different email address", "verification_code_error": "Verification code is invalid or expired, please get a new one", "email_verification": "Email Verification", "enter_email_for_verification": "Enter your email address to receive verification code", "complete_registration": "Complete Registration", "verification_code": "Verification Code", "verification_code_required": "Please enter verification code", "verification_code_invalid": "Invalid verification code", "send_verification_code": "Send Verification Code", "send_code": "Send Code", "verify_code": "Verify Code", "resend_code": "Resend", "change_email": "Change Email", "email_verified": "<PERSON><PERSON>", "back_to_email": "Back to Email Verification", "go_to_signin": "Go to Sign In", "full_name": "Full Name", "reset_password": "Reset Password", "set_new_password": "Set New Password", "enter_email_for_reset": "Enter your email address to receive password reset code", "enter_email_and_new_password": "Enter email, verification code and new password", "enter_new_password": "Enter your new password", "new_password": "New Password", "password_reset_successful": "Password Reset Successful", "password_reset_complete": "Your password has been successfully reset. Please sign in with your new password", "navigation": {"home": "Home", "profile": "Profile", "settings": "Settings"}, "continue_with_google": "Continue with Google", "continue_with_github": "Continue with GitHub", "oauth_failed_error": "<PERSON><PERSON><PERSON> login failed"}, "navigation": {"dashboard": "Dashboard", "api_keys": "API Keys", "wallet": "Wallet", "tools": "Tools", "models": "Models", "profile": "Profile", "settings": "Settings", "documentation": "Documentation", "support": "Support"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "overview": "Overview", "statistics": "Statistics", "recent_activity": "Recent Activity", "quick_actions": "Quick Actions", "balance": "Balance", "total_requests": "Total Requests", "total_tokens": "Total Tokens", "total_cost": "Total Cost", "api_usage": "API Usage", "monthly_usage": "Monthly Usage"}, "api_keys": {"title": "API Keys", "create_key": "Create API Key", "key_name": "Key Name", "key_prefix": "Key Prefix", "key": "API Key", "status": "Status", "last_used": "Last Used", "expires_at": "Expires At", "permissions": "Permissions", "usage_logs": "Usage Logs", "billing_records": "Billing Records", "view_details": "View Details", "copy_key": "Copy Key", "revoke_key": "Revoke Key", "key_copied": "API Key copied to clipboard", "key_revoked": "API Key revoked successfully", "active": "Active", "inactive": "Inactive", "revoked": "Revoked", "never": "Never", "no_keys": "No API keys found", "create_first_key": "Create your first API key to get started", "delete_confirm": "Are you sure you want to delete this API key? This action cannot be undone.", "manage_keys": "Manage Keys", "name_placeholder": "Leave empty to auto-generate", "name_helper_text": "If left empty, a random name will be generated automatically", "generate_random_name": "Generate Random Name", "created_successfully": "API Key Created Successfully!", "important": "Important", "security_warning": "This is the only time you will see the complete API key. Please copy it now and store it securely. You won't be able to see it again.", "api_key_name": "API Key Name", "your_api_key": "Your API Key", "usage_instructions": "Usage Instructions", "usage_instruction_1": "Include this API key in your request headers", "usage_instruction_2": "Keep your API key secure and never share it publicly", "usage_instruction_3": "You can manage this key from the API Keys page (enable/disable/delete)", "saved_api_key": "I've Saved My API Key", "basic_information": "Basic Information", "usage_statistics": "Usage Statistics", "total_tokens": "Total Tokens", "total_cost": "Total Cost"}, "usage_logs": {"title": "Usage Logs", "time": "Time", "model": "Model", "type": "Type", "tokens": "Tokens", "cost": "Cost", "request_id": "Request ID", "endpoint": "Endpoint", "method": "Method", "status_code": "Status Code", "duration": "Duration", "no_logs": "No usage logs found", "filter_by_date": "Filter by Date", "date_range": "Date Range", "start_date": "Start Date", "end_date": "End Date", "last_7_days": "Last 7 days", "last_30_days": "Last 30 days", "last_90_days": "Last 90 days", "last_year": "Last year", "this_month": "This month", "last_month": "Last month", "custom_range": "Custom range"}, "billing": {"title": "Billing Records", "amount": "Amount", "transaction_type": "Transaction Type", "balance_before": "Balance Before", "balance_after": "Balance After", "timestamp": "Timestamp", "no_records": "No billing records found", "charge": "Charge", "refund": "Refund", "adjustment": "Adjustment"}, "settings": {"title": "Settings", "general": "General", "security": "Security", "notifications": "Notifications", "language": "Language", "theme": "Theme", "timezone": "Timezone", "save_changes": "Save Changes", "changes_saved": "Changes saved successfully"}, "errors": {"network_error": "Network error occurred", "server_error": "Server error occurred", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "not_found": "Resource not found", "validation_error": "Validation error", "unknown_error": "Unknown error occurred"}, "date": {"formats": {"short": "MM/DD/YYYY", "long": "MMMM DD, YYYY", "datetime": "MM/DD/YYYY HH:mm:ss"}}, "quota": {"title": "Quota Configuration", "management": "Quota Management", "create_quota": "Create <PERSON><PERSON><PERSON>", "current_quotas": "Current Quotas", "add_quota": "<PERSON><PERSON>", "add_new_quota": "Add New Quota", "quota_type": "Quota Type", "period": "Period", "period_type": "Period Type", "limit_value": "Limit Value", "used_value": "Used", "remaining": "Remaining", "usage_rate": "Usage Rate", "reset_time": "Reset Time", "no_quotas": "No quota configurations", "no_quota_limits": "No quota limits. Click \"Add Quota\" button to create new quota limits.", "quota_types": {"requests": "Requests", "tokens": "Tokens", "cost": "Cost"}, "periods": {"total": "Total Limit (No Period)", "minute": "Per <PERSON>", "hour": "Per Hour", "day": "Per Day", "month": "Per Month"}, "status": {"active": "Active", "inactive": "Inactive"}, "errors": {"fetch_failed": "Failed to fetch quota information", "create_failed": "Failed to create quota", "delete_failed": "Failed to delete quota", "duplicate_quota": "Quota already exists, only one quota can be set per type and period combination"}, "helper_text": {"total_quota": "Total limit: no time period restriction"}}}