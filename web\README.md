## Minimal UI ([Free version](https://free.minimals.cc/))

![license](https://img.shields.io/badge/license-MIT-blue.svg)

![preview](public/assets/images/minimal-free-preview.jpg)

> Free React Admin Dashboard made with Material-UI components and React + Vite.js.

## Pages

- [Dashboard](https://free.minimals.cc/)
- [Users](https://free.minimals.cc/user)
- [Products](https://free.minimals.cc/products)
- [Blog](https://free.minimals.cc/blog)
- [Sign in](https://free.minimals.cc/sign-in)
- [Not found](https://free.minimals.cc/404)

## Quick start

- Clone the repo: `git clone https://github.com/minimal-ui-kit/material-kit-react.git`
- Recommended: `Node.js v20.x`
- **Install:** `npm i` or `yarn install`
- **Start:** `npm run dev` or `yarn dev`
- **Build:** `npm run build` or `yarn build`
- Open browser: `http://localhost:3039`

## Upgrade to PRO Version

| Minimal Free                | [Minimal Pro](https://material-ui.com/store/items/minimal-dashboard/)                                   |
| :-------------------------- | :------------------------------------------------------------------------------------------------------ |
| **6** Pages                 | **70+** Pages                                                                                           |
| **Partial** theme customize | **Fully** theme customize                                                                               |
| -                           | **Next.js** version                                                                                     |
| -                           | **TypeScript** version (Standard Plus and Extended license)                                             |
| -                           | Design **Figma** file (Standard Plus and Extended license)                                              |
| -                           | Authentication with **Amplify**, **Auth0**, **JWT**, **Firebase** and **Supabase**                      |
| -                           | Light/dark mode, right-to-left, form validation... ([+more components](https://minimals.cc/components)) |
| -                           | Complete users flows                                                                                    |
| -                           | 1 year of free updates / 6 months of technical support                                                  |
| -                           | Learn more: [Package & license](https://docs.minimals.cc/package)                                       |

## License

Distributed under the [MIT](https://github.com/minimal-ui-kit/minimal.free/blob/main/LICENSE.md) license.

## Contact us

Email: <EMAIL>
