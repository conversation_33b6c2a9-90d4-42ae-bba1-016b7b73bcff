import React from 'react';
import {
  <PERSON>,
  Card,
  CardHeader,
  CardContent,
  Button,
  Typography,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as VisibilityIcon,
  ContentCopy as CopyIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuthContext } from '../../../contexts/auth-context';

// 使用新的优化工具
import { useApiKeys, useDataMutation } from '../../../hooks/useData';
import { useForm, validationRules } from '../../../hooks/useForm';
import { FormDialog, ConfirmDialog, useDialog } from '../../../components/dialog/common-dialog';
import { ErrorDisplay, useErrorHandler } from '../../../components/error/error-boundary';
import { Pagination, usePagination } from '../../../components/pagination/pagination';
import { api } from '../../../services/api';

// API密钥类型定义
interface ApiKey {
  id: number;
  name: string;
  key: string;
  status: 'active' | 'inactive' | 'expired';
  created_at: string;
  expires_at?: string;
  last_used_at?: string;
}

// 创建API密钥表单数据类型
interface CreateApiKeyForm {
  name: string;
}

// 优化后的API密钥视图组件
export function ApiKeysViewOptimized() {
  const { t } = useTranslation();
  const { state } = useAuthContext();
  
  // 使用优化的数据获取Hook
  const {
    data: apiKeys = [],
    loading,
    error: fetchError,
    refresh,
  } = useApiKeys(state.user?.id);

  // 使用分页Hook
  const pagination = usePagination({
    initialPageSize: 10,
    total: apiKeys.length,
  });

  // 使用错误处理Hook
  const { error, handleError, clearError } = useErrorHandler();

  // 使用对话框Hook
  const createDialog = useDialog();
  const deleteDialog = useDialog();
  const [selectedApiKey, setSelectedApiKey] = React.useState<ApiKey | null>(null);

  // 使用表单Hook
  const createForm = useForm<CreateApiKeyForm>({
    initialValues: { name: '' },
    validationRules: {
      name: validationRules.maxLength(100, t('validation.name_too_long')),
    },
    onSubmit: async (values) => {
      await createApiKey.mutate({
        user_id: state.user?.id,
        name: values.name.trim() || undefined,
      });
      createForm.reset();
      createDialog.closeDialog();
    },
  });

  // 使用数据变更Hook
  const createApiKey = useDataMutation(
    (data: any) => api.post('/admin/api-keys/', data),
    {
      onSuccess: () => {
        refresh(); // 刷新列表
      },
      onError: handleError,
      invalidateCache: [`api-keys-${state.user?.id}`],
    }
  );

  const deleteApiKey = useDataMutation(
    (id: number) => api.delete(`/admin/api-keys/${id}`),
    {
      onSuccess: () => {
        refresh(); // 刷新列表
        deleteDialog.closeDialog();
        setSelectedApiKey(null);
      },
      onError: handleError,
      invalidateCache: [`api-keys-${state.user?.id}`],
    }
  );

  // 复制API密钥到剪贴板
  const handleCopyKey = async (key: string) => {
    try {
      await navigator.clipboard.writeText(key);
      // 这里可以添加成功提示
    } catch (err) {
      handleError(t('error.copy_failed'));
    }
  };

  // 处理删除API密钥
  const handleDeleteClick = (apiKey: ApiKey) => {
    setSelectedApiKey(apiKey);
    deleteDialog.openDialog();
  };

  const handleDeleteConfirm = () => {
    if (selectedApiKey) {
      deleteApiKey.mutate(selectedApiKey.id);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'expired':
        return 'error';
      default:
        return 'default';
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // 分页后的数据
  const paginatedApiKeys = React.useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return apiKeys.slice(startIndex, endIndex);
  }, [apiKeys, pagination.page, pagination.pageSize]);

  // 更新分页总数
  React.useEffect(() => {
    pagination.setTotal(apiKeys.length);
  }, [apiKeys.length, pagination]);

  return (
    <Box sx={{ p: 3 }}>
      {/* 错误显示 */}
      <ErrorDisplay error={error || fetchError} onClose={clearError} />

      {/* 页面标题和操作 */}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h4">{t('api_keys.title')}</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={createDialog.openDialog}
          disabled={loading}
        >
          {t('api_keys.create')}
        </Button>
      </Stack>

      {/* API密钥列表 */}
      <Card>
        <CardHeader
          title={t('api_keys.list')}
          action={
            <Button onClick={refresh} disabled={loading}>
              {t('common.refresh')}
            </Button>
          }
        />
        <CardContent>
          {loading ? (
            <Typography>{t('common.loading')}</Typography>
          ) : apiKeys.length === 0 ? (
            <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
              {t('api_keys.no_keys')}
            </Typography>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('api_keys.name')}</TableCell>
                      <TableCell>{t('api_keys.key')}</TableCell>
                      <TableCell>{t('api_keys.status')}</TableCell>
                      <TableCell>{t('api_keys.created_at')}</TableCell>
                      <TableCell>{t('api_keys.last_used')}</TableCell>
                      <TableCell align="right">{t('common.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paginatedApiKeys.map((apiKey: ApiKey) => (
                      <TableRow key={apiKey.id}>
                        <TableCell>
                          <Typography variant="body2">
                            {apiKey.name || t('api_keys.unnamed')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {apiKey.key.substring(0, 12)}****
                            </Typography>
                            <Tooltip title={t('common.copy')}>
                              <IconButton
                                size="small"
                                onClick={() => handleCopyKey(apiKey.key)}
                              >
                                <CopyIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Stack>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={t(`api_keys.status_${apiKey.status}`)}
                            color={getStatusColor(apiKey.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(apiKey.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {apiKey.last_used_at ? formatDate(apiKey.last_used_at) : t('common.never')}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Stack direction="row" spacing={1}>
                            <Tooltip title={t('common.view')}>
                              <IconButton size="small">
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.edit')}>
                              <IconButton size="small">
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t('common.delete')}>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteClick(apiKey)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* 分页组件 */}
              <Pagination
                page={pagination.page}
                totalPages={pagination.totalPages}
                total={pagination.total}
                pageSize={pagination.pageSize}
                onPageChange={pagination.setPage}
                onPageSizeChange={pagination.setPageSize}
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* 创建API密钥对话框 */}
      <FormDialog
        open={createDialog.open}
        onClose={createDialog.closeDialog}
        onSubmit={createForm.handleSubmit}
        title={t('api_keys.create')}
        loading={createForm.isSubmitting || createApiKey.loading}
        disabled={!createForm.isValid}
      >
        <Stack spacing={3} sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {t('api_keys.create_description')}
          </Typography>
          
          {/* 这里可以添加表单字段 */}
          {/* 由于篇幅限制，省略具体的表单实现 */}
        </Stack>
      </FormDialog>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={deleteDialog.open}
        onClose={deleteDialog.closeDialog}
        onConfirm={handleDeleteConfirm}
        title={t('api_keys.delete_confirm_title')}
        message={t('api_keys.delete_confirm_message', { name: selectedApiKey?.name || t('api_keys.unnamed') })}
        confirmText={t('common.delete')}
        confirmColor="error"
        loading={deleteApiKey.loading}
      />
    </Box>
  );
}
