{
  "compilerOptions": {
    /* B<PERSON>ler */
    "baseUrl": ".",
    "module": "ESNext",
    "jsx": "react-jsx",
    "allowJs": true,
    "resolveJsonModule": true,

    /* Build */
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleResolution": "bundler",
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "incremental": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "isolatedModules": true,

    /* Linting */
    "strict": true,
    "noEmit": true,
    "strictNullChecks": true
  },
  "include": ["src"],
  "exclude": ["node_modules"],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
