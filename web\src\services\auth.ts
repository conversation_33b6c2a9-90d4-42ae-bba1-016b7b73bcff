import api from './api';
import TokenStorage from '../utils/token-storage';

// 认证相关的类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: UserInfo;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface RegisterResponse {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  message: string;
  created_at: string;
}

// 验证码相关接口
export interface SendVerificationCodeRequest {
  email: string;
  type: 'register' | 'password_reset';
}

export interface SendVerificationCodeResponse {
  message: string;
  expires_in: number;
}

export interface VerifyCodeRequest {
  email: string;
  code: string;
  type: 'register' | 'password_reset';
}

export interface VerifyCodeResponse {
  valid: boolean;
  message: string;
}

export interface RegisterWithCodeRequest {
  username: string;
  email: string;
  password: string;
  verification_code: string;
}

export interface ResetPasswordRequest {
  email: string;
  new_password: string;
  verification_code: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface UserInfo {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  balance?: number;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

export interface RechargeRequest {
  amount: number;
  description?: string;
}

// OAuth登录请求类型
export interface OAuthLoginRequest {
  provider: string;
  code: string;
  state: string;
}

// OAuth URL响应类型
export interface OAuthURLResponse {
  auth_url: string;
  state: string;
}

// 认证服务类
export class AuthService {
  /**
   * 用户登录 - 不需要token认证
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // 使用noAuth方法，确保不会注入token
      const response = await api.noAuth.post<LoginResponse>('/auth/login', credentials);

      if (response.success && response.data) {
        // 存储认证数据
        TokenStorage.setAuthData(
          response.data.access_token,
          response.data.refresh_token,
          response.data.user
        );

        return response.data;
      }

      throw new Error(response.error?.message || 'Login failed');
    } catch (error: any) {
      // 处理401错误（用户名或密码错误）
      if (error.response?.status === 401) {
        throw new Error('INVALID_CREDENTIALS');
      }

      // 处理其他错误
      if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }

      throw new Error(error.message || 'Login failed');
    }
  }

  /**
   * 用户注册 - 不需要token认证
   */
  static async register(userData: RegisterRequest): Promise<RegisterResponse> {
    try {
      // 使用noAuth方法，确保不会注入token
      const response = await api.noAuth.post<RegisterResponse>('/auth/register', userData);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error?.message || 'Registration failed');
    } catch (error: any) {
      // 处理409冲突错误（用户已存在）
      if (error.response?.status === 409) {
        const errorCode = error.response?.data?.error?.code;
        if (errorCode === 'USER_EXISTS') {
          const customError = new Error('User already exists');
          (customError as any).code = 'USER_ALREADY_EXISTS';
          throw customError;
        }
      }

      // 其他错误
      throw new Error(error.response?.data?.error?.message || error.message || 'Registration failed');
    }
  }

  /**
   * 刷新访问令牌 - 不需要token认证
   */
  static async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    // 使用noAuth方法，确保不会注入token
    const response = await api.noAuth.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });

    if (response.success && response.data) {
      // 更新存储的token
      TokenStorage.setAccessToken(response.data.access_token);
      TokenStorage.setRefreshToken(response.data.refresh_token);

      return response.data;
    }

    throw new Error(response.error?.message || 'Token refresh failed');
  }

  /**
   * 获取用户资料
   */
  static async getProfile(): Promise<UserProfile> {
    const response = await api.get<UserProfile>('/auth/profile');

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error?.message || 'Failed to get user profile');
  }

  /**
   * 修改密码
   */
  static async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
    const response = await api.post('/auth/change-password', passwordData);

    if (!response.success) {
      throw new Error(response.error?.message || 'Password change failed');
    }
  }

  /**
   * 充值余额
   */
  static async recharge(rechargeData: RechargeRequest): Promise<UserProfile> {
    const response = await api.post<UserProfile>('/auth/recharge', {
      amount: rechargeData.amount,
      description: rechargeData.description || '用户充值'
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error?.message || 'Failed to recharge');
  }

  /**
   * 用户登出
   */
  static logout(): void {
    // 清除本地存储的认证信息
    TokenStorage.clearAuthData();

    // 跳转到登录页
    window.location.href = '/sign-in';
  }

  /**
   * 检查用户是否已登录
   */
  static isAuthenticated(): boolean {
    return TokenStorage.hasValidAuthData();
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): UserInfo | null {
    return TokenStorage.getUserInfo();
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return TokenStorage.getAccessToken();
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return TokenStorage.getRefreshToken();
  }

  /**
   * 检查token是否即将过期（提前5分钟刷新）
   */
  static shouldRefreshToken(): boolean {
    const token = this.getAccessToken();
    if (!token) return false;

    try {
      // 解析JWT token的payload
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // 转换为毫秒
      const currentTime = Date.now();
      const fiveMinutes = 5 * 60 * 1000; // 5分钟

      // 如果token在5分钟内过期，则需要刷新
      return expirationTime - currentTime < fiveMinutes;
    } catch (error) {
      console.error('Failed to parse token:', error);
      return true; // 解析失败时也尝试刷新
    }
  }

  /**
   * 自动刷新token（如果需要）
   */
  static async autoRefreshToken(): Promise<void> {
    if (this.shouldRefreshToken()) {
      const refreshToken = this.getRefreshToken();
      if (refreshToken) {
        try {
          await this.refreshToken(refreshToken);
        } catch (error) {
          console.error('Auto refresh token failed:', error);
          // 不要立即logout，让调用者决定如何处理
          throw error;
        }
      }
    }
  }

  /**
   * 获取OAuth认证URL
   */
  static async getOAuthURL(provider: string): Promise<OAuthURLResponse> {
    const response = await api.noAuth.get<OAuthURLResponse>(`/auth/oauth/${provider}/url`);

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error?.message || 'Failed to get OAuth URL');
  }

  /**
   * 处理OAuth回调
   */
  static async handleOAuthCallback(request: OAuthLoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.noAuth.post<LoginResponse>(`/auth/oauth/${request.provider}/callback`, request);

      if (response.success && response.data) {
        // 存储认证数据
        TokenStorage.setAuthData(
          response.data.access_token,
          response.data.refresh_token,
          response.data.user
        );

        return response.data;
      }

      throw new Error(response.error?.message || 'OAuth login failed');
    } catch (error: any) {
      // 处理OAuth特定错误
      if (error.response?.status === 401) {
        throw new Error('OAUTH_FAILED');
      }

      // 处理其他错误
      if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }

      throw new Error(error.message || 'OAuth login failed');
    }
  }

  /**
   * OAuth登录（完整流程）
   */
  static async oauthLogin(provider: string): Promise<void> {
    try {
      // 获取认证URL
      const { auth_url } = await this.getOAuthURL(provider);

      // 重定向到OAuth提供商
      window.location.href = auth_url;
    } catch (error) {
      console.error('OAuth login failed:', error);
      throw error;
    }
  }

  /**
   * 发送验证码
   */
  static async sendVerificationCode(request: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> {
    const response = await api.noAuth.post<SendVerificationCodeResponse>('/auth/send-verification-code', request);

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error?.message || 'Failed to send verification code');
  }

  /**
   * 验证验证码
   */
  static async verifyCode(request: VerifyCodeRequest): Promise<VerifyCodeResponse> {
    const response = await api.noAuth.post<VerifyCodeResponse>('/auth/verify-code', request);

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error?.message || 'Failed to verify code');
  }

  /**
   * 带验证码的用户注册
   */
  static async registerWithCode(userData: RegisterWithCodeRequest): Promise<RegisterResponse> {
    try {
      const response = await api.noAuth.post<RegisterResponse>('/auth/register-with-code', userData);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error?.message || 'Registration failed');
    } catch (error: any) {
      // 处理409冲突错误（用户已存在）
      if (error.response?.status === 409) {
        const errorCode = error.response?.data?.error?.code;
        if (errorCode === 'USER_EXISTS') {
          const customError = new Error('User already exists');
          (customError as any).code = 'USER_ALREADY_EXISTS';
          throw customError;
        }
      }

      // 处理400错误（验证码错误等）
      if (error.response?.status === 400) {
        const errorCode = error.response?.data?.error?.code;
        if (errorCode === 'INVALID_VERIFICATION_CODE') {
          const customError = new Error('Verification code error');
          (customError as any).code = 'VERIFICATION_CODE_ERROR';
          throw customError;
        }
      }

      // 其他错误
      throw new Error(error.response?.data?.error?.message || error.message || 'Registration failed');
    }
  }

  /**
   * 重置密码
   */
  static async resetPassword(request: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    try {
      const response = await api.noAuth.post<ResetPasswordResponse>('/auth/reset-password', request);

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error?.message || 'Password reset failed');
    } catch (error: any) {
      // 处理400错误（验证码错误等）
      if (error.response?.status === 400) {
        const errorCode = error.response?.data?.error?.code;
        if (errorCode === 'INVALID_VERIFICATION_CODE') {
          const customError = new Error('Verification code error');
          (customError as any).code = 'VERIFICATION_CODE_ERROR';
          throw customError;
        }
      }

      // 其他错误
      throw new Error(error.response?.data?.error?.message || error.message || 'Password reset failed');
    }
  }
}

export default AuthService;
